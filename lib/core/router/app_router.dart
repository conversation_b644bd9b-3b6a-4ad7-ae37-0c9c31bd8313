import 'package:flutter/material.dart';
import 'package:bill_book/features/splash/view/splash_screen.dart';
import 'package:bill_book/features/home/<USER>/home_screen.dart';
import 'package:bill_book/features/stock_summary/view/stock_summary_screen.dart';
import 'package:bill_book/features/reports/view/reports_screen.dart';
import 'package:bill_book/features/cash_bank/view/cash_bank_screen.dart';
import 'package:bill_book/features/import_export/view/import_export_screen.dart';
import 'package:bill_book/features/create_party/view/create_party_screen.dart';
import 'package:bill_book/features/adjust_stock/view/adjust_stock_screen.dart';
import 'package:bill_book/features/settings/view/item_settings_screen.dart';
import 'package:bill_book/features/account_settings/view/account_settings_screen.dart';
import 'package:bill_book/features/create_item/view/create_item_screen.dart';
import 'package:bill_book/features/reminder_settings/view/reminder_settings_screen.dart';
import 'package:bill_book/features/item_details/view/item_details_screen.dart';
import 'package:bill_book/core/router/app_routes.dart';

import 'custom_page_route.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case SplashRoute.name:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case HomeRoute.name:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case StockSummaryRoute.name:
        return MaterialPageRoute(builder: (_) => const StockSummaryScreen());
      case ReportsRoute.name:
        return MaterialPageRoute(builder: (_) => const ReportsScreen());
      case CashBankRoute.name:
        return MaterialPageRoute(builder: (_) => const CashBankScreen());
      case ImportExportRoute.name:
        return MaterialPageRoute(builder: (_) => const ImportExportScreen());
      case CreatePartyRoute.name:
        return MaterialPageRoute(builder: (_) => const CreatePartyScreen());
      case AdjustStockRoute.name:
        final item = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(builder: (_) => AdjustStockScreen(item: item));
      case ItemSettingsRoute.name:
        return SlidePageRoute(
          child: const ItemSettingsScreen(),
          settings: settings,
        );
      case CreateItemRoute.name:
        return SlidePageRoute(
          child: const CreateItemScreen(),
          settings: settings,
        );
      case AccountSettingsRoute.name:
        return SlidePageRoute(
          child: const AccountSettingsScreen(),
          settings: settings,
        );
      case ReminderSettingsRoute.name:
        return SlidePageRoute(
          child: const ReminderSettingsScreen(),
          settings: settings,
        );
      case ItemDetailsRoute.name:
        final item = settings.arguments as Map<String, dynamic>;
        return SlidePageRoute(
          child: ItemDetailsScreen(item: item),
          settings: settings,
        );
      default:
        return MaterialPageRoute(
          builder: (_) =>
              const Scaffold(body: Center(child: Text('Page not found'))),
        );
    }
  }
}
