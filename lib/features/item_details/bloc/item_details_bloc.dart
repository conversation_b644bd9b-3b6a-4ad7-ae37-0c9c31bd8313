import 'package:flutter_bloc/flutter_bloc.dart';
import 'item_details_event.dart';
import 'item_details_state.dart';

class ItemDetailsBloc extends Bloc<ItemDetailsEvent, ItemDetailsState> {
  ItemDetailsBloc() : super(const ItemDetailsState()) {
    on<LoadItemDetails>(_onLoadItemDetails);
    on<TabChanged>(_onTabChanged);
    on<AdjustStockPressed>(_onAdjustStockPressed);
    on<SharePressed>(_onSharePressed);
    on<EditPressed>(_onEditPressed);
    on<DeletePressed>(_onDeletePressed);
  }

  Future<void> _onLoadItemDetails(
    LoadItemDetails event,
    Emitter<ItemDetailsState> emit,
  ) async {
    emit(state.copyWith(status: ItemDetailsStatus.loading));

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(milliseconds: 300));

      emit(state.copyWith(status: ItemDetailsStatus.loaded, item: event.item));
    } catch (e) {
      emit(
        state.copyWith(
          status: ItemDetailsStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  void _onTabChanged(TabChanged event, Emitter<ItemDetailsState> emit) {
    emit(state.copyWith(currentTabIndex: event.tabIndex));
  }

  void _onAdjustStockPressed(
    AdjustStockPressed event,
    Emitter<ItemDetailsState> emit,
  ) {
    // The navigation will be handled in the UI layer
    // This event can be used to trigger any necessary state changes
    // before navigation or to handle post-navigation updates
  }

  void _onSharePressed(SharePressed event, Emitter<ItemDetailsState> emit) {
    // Handle share action
  }

  void _onEditPressed(EditPressed event, Emitter<ItemDetailsState> emit) {
    // Handle edit action
    // This could navigate to edit item screen
  }

  void _onDeletePressed(DeletePressed event, Emitter<ItemDetailsState> emit) {
    // Handle delete action
    // This could show confirmation dialog
  }
}
